package com.llq.hs.service.crawler.VJ.interceptor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 2025/07/22
 **/
@Component
public class JwtTokenInterceptor extends HandlerInterceptorAdapter {

    @Value("${lcc.web.token}")
    private String TOKEN;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {
        // 放行OPTIONS请求
        if("OPTIONS".equals(request.getMethod())) {
            return true;
        }
        //String clientIp = ServletUtil.getClientIP(request);
        String token = request.getParameter("token");
        boolean check =  TOKEN.equals(token);
        if (!check){
            response.getWriter().println("You do not have permission to access !");
        }
        return check;
    }
}
